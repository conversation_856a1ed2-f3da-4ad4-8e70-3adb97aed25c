# AI Coding Agent VS Code Extension

This VS Code extension integrates with the AI Coding Agent bridge to provide AI-powered coding assistance directly in VS Code.

## Features

- Run AI Coding Agent tasks directly from VS Code
- Stop running tasks
- Check the status of the AI Coding Agent bridge
- Chat interface for interacting with the AI Coding Agent

## Requirements

- AI Coding Agent bridge running locally or remotely
- Node.js 16 or higher for development

## Development Setup

1. Install dependencies:
   ```bash
   cd vscode-extension
   npm install
   ```

2. Compile the extension:
   ```bash
   npm run compile
   ```

3. Run the extension in a new VS Code window:
   ```bash
   npm run watch
   # Press F5 in VS Code to start debugging
   ```

## Extension Settings

This extension contributes the following settings:

* `aiCodingAgent.bridgeHost`: Host address for the AI Coding Agent bridge (default: "localhost")
* `aiCodingAgent.bridgePort`: Port number for the AI Coding Agent bridge (default: 8080)
* `aiCodingAgent.modelName`: Model name to use for the AI Coding Agent (default: "claude-3-opus-20240229")

## Commands

This extension provides the following commands:

* `AI Coding Agent: Run Agent`: Run the AI Coding Agent with a task description
* `AI Coding Agent: Stop Agent`: Stop the currently running AI Coding Agent task
* `AI Coding Agent: Check Status`: Check the status of the AI Coding Agent bridge
* `AI Coding Agent: Open Chat`: Open the chat interface for interacting with the AI Coding Agent

{"name": "ai-coding-agent", "displayName": "AI Coding Agent", "description": "VS Code extension for AI Coding Agent that integrates with SWE-Agent", "version": "0.1.0", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onCommand:ai-coding-agent.runAgent", "onCommand:ai-coding-agent.stopAgent", "onCommand:ai-coding-agent.checkStatus"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-coding-agent.runAgent", "title": "AI Coding Agent: Run Agent"}, {"command": "ai-coding-agent.stopAgent", "title": "AI Coding Agent: Stop Agent"}, {"command": "ai-coding-agent.checkStatus", "title": "AI Coding Agent: Check Status"}], "configuration": {"title": "AI Coding Agent", "properties": {"aiCodingAgent.bridgeHost": {"type": "string", "default": "localhost", "description": "Host address for the AI Coding Agent bridge"}, "aiCodingAgent.bridgePort": {"type": "number", "default": 8080, "description": "Port number for the AI Coding Agent bridge"}, "aiCodingAgent.modelName": {"type": "string", "default": "claude-3-opus-20240229", "description": "Model name to use for the AI Coding Agent"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.4", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "eslint": "^8.1.0", "glob": "^7.1.7", "mocha": "^9.1.3", "typescript": "^4.4.4", "vscode-test": "^1.6.1"}, "dependencies": {"axios": "^0.24.0", "socket.io-client": "^4.4.0"}, "keywords": [], "author": "", "license": "ISC"}
import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';

// Status bar item to show the current status of the AI Coding Agent
let statusBarItem: vscode.StatusBarItem;

// Output channel for logging
let outputChannel: vscode.OutputChannel;

// Bridge client for communicating with the AI Coding Agent bridge
let bridgeClient: BridgeClient;

/**
 * Activate the extension
 */
export function activate(context: vscode.ExtensionContext) {
    // Create output channel
    outputChannel = vscode.window.createOutputChannel('AI Coding Agent');
    outputChannel.appendLine('AI Coding Agent extension is now active');
    context.subscriptions.push(outputChannel);

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = '$(rocket) AI Agent';
    statusBarItem.tooltip = 'AI Coding Agent';
    statusBarItem.command = 'ai-coding-agent.checkStatus';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);
    
    // Initialize bridge client
    bridgeClient = new BridgeClient(outputChannel, updateStatusBar);
    
    // Register commands
    const runAgentCommand = vscode.commands.registerCommand('ai-coding-agent.runAgent', runAgent);
    const stopAgentCommand = vscode.commands.registerCommand('ai-coding-agent.stopAgent', stopAgent);
    const checkStatusCommand = vscode.commands.registerCommand('ai-coding-agent.checkStatus', checkStatus);

    context.subscriptions.push(runAgentCommand);
    context.subscriptions.push(stopAgentCommand);
    context.subscriptions.push(checkStatusCommand);
    
    // Connect to the bridge
    bridgeClient.connect();
}

/**
 * Deactivate the extension
 */
export function deactivate() {
    if (bridgeClient) {
        bridgeClient.disconnect();
    }
    outputChannel.appendLine('AI Coding Agent extension deactivated');
}

/**
 * Update the status bar item based on the current status
 */
function updateStatusBar(status: 'connected' | 'disconnected' | 'running' | 'error') {
    switch (status) {
        case 'connected':
            statusBarItem.text = '$(rocket) AI Agent: Ready';
            statusBarItem.backgroundColor = undefined;
            break;
        case 'disconnected':
            statusBarItem.text = '$(circle-slash) AI Agent: Disconnected';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            break;
        case 'running':
            statusBarItem.text = '$(sync~spin) AI Agent: Running';
            statusBarItem.backgroundColor = undefined;
            break;
        case 'error':
            statusBarItem.text = '$(error) AI Agent: Error';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
            break;
    }
}

/**
 * Run the AI Coding Agent
 */
async function runAgent() {
    // Get the current workspace folder
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
        vscode.window.showErrorMessage('No workspace folder is open. Please open a folder first.');
        return;
    }

    // Get the repository path
    const repoPath = workspaceFolders[0].uri.fsPath;

    // Get the problem statement from the user
    const problemStatement = await vscode.window.showInputBox({
        prompt: 'Enter a task description for the AI Coding Agent',
        placeHolder: 'e.g., Create a function to calculate the Fibonacci sequence'
    });

    if (!problemStatement) {
        return; // User cancelled
    }

    // Get configuration
    const config = vscode.workspace.getConfiguration('aiCodingAgent');
    const modelName = config.get<string>('modelName') || 'claude-3-opus-20240229';

    // Update status
    outputChannel.show();

    try {
        const response = await bridgeClient.runAgent(problemStatement, repoPath, modelName);

        if (response.status === 'success') {
            vscode.window.showInformationMessage('AI Coding Agent started successfully');
        } else {
            vscode.window.showErrorMessage(`Failed to start AI Coding Agent: ${response.error || 'Unknown error'}`);
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Error running AI Coding Agent: ${error.message}`);
    }
}

/**
 * Stop the AI Coding Agent
 */
async function stopAgent() {
    try {
        const response = await bridgeClient.stopAgent();

        if (response.status === 'success') {
            vscode.window.showInformationMessage('AI Coding Agent stopped successfully');
        } else {
            vscode.window.showErrorMessage(`Failed to stop AI Coding Agent: ${response.error || 'Unknown error'}`);
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Error stopping AI Coding Agent: ${error.message}`);
    }
}

/**
 * Check the status of the AI Coding Agent bridge
 */
async function checkStatus() {
    try {
        const response = await bridgeClient.checkStatus();

        if (response.status === 'ok') {
            vscode.window.showInformationMessage('AI Coding Agent bridge is running');
            updateStatusBar('connected');
        } else {
            vscode.window.showWarningMessage('AI Coding Agent bridge is not responding correctly');
            updateStatusBar('error');
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Cannot connect to AI Coding Agent bridge: ${error.message}`);
        updateStatusBar('disconnected');
    }
}

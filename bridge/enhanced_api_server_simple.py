"""
Simplified Enhanced API server for the AI Coding Agent bridge with session management.
"""

import logging
import os
from typing import Any

from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room, leave_room

# Set up basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Track WebSocket connections
connected_clients = {}

# Lazy imports to avoid circular dependencies
session_manager = None
SessionConfig = None
SessionStatus = None
config = None


def get_session_manager():
    """Get session manager with lazy initialization."""
    global session_manager, SessionConfig, SessionStatus
    if session_manager is None:
        from bridge.session_manager import session_manager, SessionConfig, SessionStatus
    return session_manager, SessionConfig, SessionStatus


def get_config():
    """Get config with lazy initialization."""
    global config
    if config is None:
        from bridge.config import config
    return config


def get_swe_agent():
    """Get SWE-Agent interface with lazy initialization."""
    from bridge.swe_agent_interface import SWEAgentInterface
    return SWEAgentInterface()


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok", "version": "2.0"})


@app.route('/api/sessions', methods=['POST'])
def create_session():
    """Create a new agent session."""
    try:
        sm, SessionConfig, SessionStatus = get_session_manager()
        
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400
        
        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400
        
        # Create session configuration
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-3-opus-20240229'),
            repo_path=repo_path,
            problem_statement=problem_statement,
            tools=data.get('tools'),
            environment=data.get('environment'),
            retry_config=data.get('retry_config')
        )
        
        # Create session
        session_id = sm.create_session(config_data)
        
        return jsonify({
            "session_id": session_id,
            "status": "created"
        }), 201
    
    except Exception as e:
        logger.exception("Error creating session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions', methods=['GET'])
def list_sessions():
    """List all sessions."""
    try:
        sm, SessionConfig, SessionStatus = get_session_manager()
        
        status_filter = request.args.get('status')
        status_enum = None
        
        if status_filter:
            try:
                status_enum = SessionStatus(status_filter)
            except ValueError:
                return jsonify({"error": f"Invalid status: {status_filter}"}), 400
        
        sessions = sm.list_sessions(status_enum)
        return jsonify({
            "sessions": [session.to_dict() for session in sessions]
        })
    
    except Exception as e:
        logger.exception("Error listing sessions")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get a specific session."""
    try:
        sm, SessionConfig, SessionStatus = get_session_manager()
        
        session = sm.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify(session.to_dict())
    
    except Exception as e:
        logger.exception("Error getting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/start', methods=['POST'])
def start_session(session_id):
    """Start a session."""
    try:
        sm, SessionConfig, SessionStatus = get_session_manager()
        
        session = sm.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        if session.status != SessionStatus.CREATED:
            return jsonify({"error": f"Session is in {session.status.value} state"}), 400
        
        # Update session status
        sm.update_session_status(session_id, SessionStatus.RUNNING)
        
        # Start the agent (simplified for testing)
        try:
            swe_agent = get_swe_agent()
            result = swe_agent.run_agent(
                problem_statement=session.config.problem_statement,
                repo_path=session.config.repo_path,
                model_name=session.config.model_name
            )
            
            if result == "success":
                return jsonify({"status": "started"})
            else:
                sm.update_session_status(session_id, SessionStatus.FAILED, "Failed to start agent")
                return jsonify({"error": "Failed to start agent"}), 500
        except Exception as e:
            sm.update_session_status(session_id, SessionStatus.FAILED, str(e))
            return jsonify({"error": f"Agent start failed: {str(e)}"}), 500
    
    except Exception as e:
        logger.exception("Error starting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/stop', methods=['POST'])
def stop_session(session_id):
    """Stop a session."""
    try:
        sm, SessionConfig, SessionStatus = get_session_manager()
        
        session = sm.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        if session.status != SessionStatus.RUNNING:
            return jsonify({"error": f"Session is not running (status: {session.status.value})"}), 400
        
        # Stop the agent
        try:
            swe_agent = get_swe_agent()
            result = swe_agent.stop_agent()
            
            if result:
                sm.update_session_status(session_id, SessionStatus.TERMINATED)
                return jsonify({"status": "stopped"})
            else:
                return jsonify({"error": "Failed to stop agent"}), 500
        except Exception as e:
            return jsonify({"error": f"Agent stop failed: {str(e)}"}), 500
    
    except Exception as e:
        logger.exception("Error stopping session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/trajectory', methods=['GET'])
def get_session_trajectory(session_id):
    """Get session trajectory."""
    try:
        sm, SessionConfig, SessionStatus = get_session_manager()
        
        session = sm.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify({
            "session_id": session_id,
            "trajectory": session.trajectory
        })
    
    except Exception as e:
        logger.exception("Error getting session trajectory")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET'])
def get_config_endpoint():
    """Get the current configuration."""
    try:
        config = get_config()
        return jsonify(config.config)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# WebSocket event handlers
@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    client_id = request.sid
    connected_clients[client_id] = {
        'connected_at': 0,  # Will be set properly when time is available
        'subscribed_sessions': set()
    }
    logger.info(f"Client {client_id} connected")
    emit('connected', {'client_id': client_id})


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    client_id = request.sid
    if client_id in connected_clients:
        # Leave all subscribed session rooms
        for session_id in connected_clients[client_id]['subscribed_sessions']:
            leave_room(f"session_{session_id}")
        
        del connected_clients[client_id]
    
    logger.info(f"Client {client_id} disconnected")


@socketio.on('subscribe_session')
def handle_subscribe_session(data):
    """Subscribe to session updates."""
    client_id = request.sid
    session_id = data.get('session_id')
    
    if not session_id:
        emit('error', {'message': 'No session_id provided'})
        return
    
    # Verify session exists
    sm, SessionConfig, SessionStatus = get_session_manager()
    session = sm.get_session(session_id)
    if not session:
        emit('error', {'message': 'Session not found'})
        return
    
    # Join session room
    join_room(f"session_{session_id}")
    
    # Track subscription
    if client_id in connected_clients:
        connected_clients[client_id]['subscribed_sessions'].add(session_id)
    
    emit('subscribed', {'session_id': session_id})
    logger.info(f"Client {client_id} subscribed to session {session_id}")


def run_server():
    """Run the enhanced API server."""
    try:
        config = get_config()
        host = config.get("bridge", "api_host", default="localhost")
        port = config.get("bridge", "api_port", default=8080)
    except Exception:
        host = "localhost"
        port = 8080
    
    logger.info(f"Starting enhanced API server on {host}:{port}")
    socketio.run(app, host=host, port=port, debug=True, allow_unsafe_werkzeug=True)


if __name__ == "__main__":
    run_server()

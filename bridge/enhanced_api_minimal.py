"""
Minimal Enhanced API server for the AI Coding Agent bridge with session management.
This version focuses on core functionality without complex initialization issues.
"""

import logging
from flask import Flask, request, jsonify
from flask_cors import CORS

# Set up basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok", "version": "2.0-enhanced"})


@app.route('/api/sessions', methods=['POST'])
def create_session():
    """Create a new agent session."""
    try:
        # Lazy import to avoid initialization issues
        from bridge.session_manager import session_manager, SessionConfig
        
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400
        
        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400
        
        # Create session configuration
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-3-opus-20240229'),
            repo_path=repo_path,
            problem_statement=problem_statement,
            tools=data.get('tools'),
            environment=data.get('environment'),
            retry_config=data.get('retry_config')
        )
        
        # Create session
        session_id = session_manager.create_session(config_data)
        
        return jsonify({
            "session_id": session_id,
            "status": "created"
        }), 201
    
    except Exception as e:
        logger.exception("Error creating session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions', methods=['GET'])
def list_sessions():
    """List all sessions."""
    try:
        from bridge.session_manager import session_manager, SessionStatus
        
        status_filter = request.args.get('status')
        status_enum = None
        
        if status_filter:
            try:
                status_enum = SessionStatus(status_filter)
            except ValueError:
                return jsonify({"error": f"Invalid status: {status_filter}"}), 400
        
        sessions = session_manager.list_sessions(status_enum)
        return jsonify({
            "sessions": [session.to_dict() for session in sessions]
        })
    
    except Exception as e:
        logger.exception("Error listing sessions")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get a specific session."""
    try:
        from bridge.session_manager import session_manager
        
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify(session.to_dict())
    
    except Exception as e:
        logger.exception("Error getting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/start', methods=['POST'])
def start_session(session_id):
    """Start a session."""
    try:
        from bridge.session_manager import session_manager, SessionStatus
        
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        if session.status != SessionStatus.CREATED:
            return jsonify({"error": f"Session is in {session.status.value} state"}), 400
        
        # Update session status
        session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        
        # For Phase 1, we'll just simulate starting the agent
        # In Phase 2, we'll integrate with the actual SWE-Agent
        session_manager.update_session_progress(session_id, 0.1, "Initializing agent")
        
        return jsonify({"status": "started", "message": "Session started (Phase 1 simulation)"})
    
    except Exception as e:
        logger.exception("Error starting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/stop', methods=['POST'])
def stop_session(session_id):
    """Stop a session."""
    try:
        from bridge.session_manager import session_manager, SessionStatus
        
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        if session.status != SessionStatus.RUNNING:
            return jsonify({"error": f"Session is not running (status: {session.status.value})"}), 400
        
        # Stop the session
        session_manager.update_session_status(session_id, SessionStatus.TERMINATED)
        
        return jsonify({"status": "stopped"})
    
    except Exception as e:
        logger.exception("Error stopping session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/trajectory', methods=['GET'])
def get_session_trajectory(session_id):
    """Get session trajectory."""
    try:
        from bridge.session_manager import session_manager
        
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify({
            "session_id": session_id,
            "trajectory": session.trajectory
        })
    
    except Exception as e:
        logger.exception("Error getting session trajectory")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET'])
def get_config():
    """Get the current configuration."""
    try:
        from bridge.config import config
        return jsonify(config.config)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


# Legacy compatibility endpoints
@app.route('/api/run', methods=['POST'])
def legacy_run_agent():
    """Legacy endpoint for running agent (redirects to session-based approach)."""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Create a session for the legacy request
        from bridge.session_manager import session_manager, SessionConfig
        
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-3-opus-20240229'),
            repo_path=data.get('repo_path', '.'),
            problem_statement=data.get('problem_statement', 'Legacy run request'),
        )
        
        session_id = session_manager.create_session(config_data)
        
        return jsonify({
            "status": "success", 
            "session_id": session_id,
            "message": "Legacy request converted to session-based approach"
        })
    
    except Exception as e:
        logger.exception("Error in legacy run endpoint")
        return jsonify({"error": str(e)}), 500


@app.route('/api/stop', methods=['POST'])
def legacy_stop_agent():
    """Legacy endpoint for stopping agent."""
    try:
        from bridge.session_manager import session_manager, SessionStatus
        
        # Stop all running sessions (simplified for legacy compatibility)
        sessions = session_manager.list_sessions(SessionStatus.RUNNING)
        stopped_count = 0
        
        for session in sessions:
            session_manager.update_session_status(session.session_id, SessionStatus.TERMINATED)
            stopped_count += 1
        
        return jsonify({
            "status": "success" if stopped_count > 0 else "no_running_sessions",
            "stopped_sessions": stopped_count
        })
    
    except Exception as e:
        logger.exception("Error in legacy stop endpoint")
        return jsonify({"error": str(e)}), 500


def run_server():
    """Run the enhanced API server."""
    try:
        from bridge.config import config
        host = config.get("bridge", "api_host", default="localhost")
        port = config.get("bridge", "api_port", default=8080)
    except Exception:
        host = "localhost"
        port = 8080
    
    logger.info(f"Starting enhanced API server on {host}:{port}")
    app.run(host=host, port=port, debug=True)


if __name__ == "__main__":
    run_server()

"""
Integration module for the Vim plugin.
"""

import json
import socket
import threading
import logging
from typing import Dict, Any, Optional, Callable

from bridge.config import config
from bridge.swe_agent_interface import SWEAgentInterface

logger = logging.getLogger(__name__)


class VimIntegration:
    """Integration with the Vim plugin."""
    
    def __init__(self):
        """Initialize the Vim integration."""
        self.swe_agent = SWEAgentInterface()
        self.socket = None
        self.server_thread = None
        self.running = False
    
    def start_server(self, host: str = "localhost", port: int = 8081):
        """
        Start the server for Vim plugin communication.
        
        Args:
            host: Host to bind to.
            port: Port to bind to.
        """
        if self.running:
            logger.warning("Server is already running")
            return
        
        self.running = True
        self.server_thread = threading.Thread(
            target=self._run_server,
            args=(host, port),
            daemon=True
        )
        self.server_thread.start()
        logger.info(f"Vim integration server started on {host}:{port}")
    
    def stop_server(self):
        """Stop the server."""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except Exception as e:
                logger.error(f"Error closing socket: {e}")
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=1)
        
        logger.info("Vim integration server stopped")
    
    def _run_server(self, host: str, port: int):
        """
        Run the server.
        
        Args:
            host: Host to bind to.
            port: Port to bind to.
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((host, port))
            self.socket.listen(5)
            
            while self.running:
                try:
                    client_socket, address = self.socket.accept()
                    logger.info(f"Connection from {address}")
                    
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        logger.error(f"Error accepting connection: {e}")
        
        except Exception as e:
            logger.error(f"Error starting server: {e}")
        
        finally:
            if self.socket:
                self.socket.close()
    
    def _handle_client(self, client_socket: socket.socket):
        """
        Handle a client connection.
        
        Args:
            client_socket: Client socket.
        """
        try:
            # Set a timeout to avoid blocking forever
            client_socket.settimeout(60)
            
            # Receive data
            data = b""
            while True:
                chunk = client_socket.recv(4096)
                if not chunk:
                    break
                data += chunk
                
                # Check if we have received a complete message
                if data.endswith(b"\n"):
                    break
            
            if not data:
                logger.warning("No data received from client")
                return
            
            # Parse the request
            try:
                request = json.loads(data.decode("utf-8"))
                response = self._handle_request(request)
                
                # Send the response
                client_socket.sendall(json.dumps(response).encode("utf-8"))
            
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON received: {data.decode('utf-8')}")
                client_socket.sendall(json.dumps({
                    "status": "error",
                    "message": "Invalid JSON"
                }).encode("utf-8"))
        
        except Exception as e:
            logger.error(f"Error handling client: {e}")
        
        finally:
            client_socket.close()
    
    def _handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle a request from the Vim plugin.
        
        Args:
            request: Request from the Vim plugin.
            
        Returns:
            Response to send back to the Vim plugin.
        """
        command = request.get("command")
        if not command:
            return {"status": "error", "message": "No command specified"}
        
        if command == "run":
            # Run the agent on a problem statement
            problem_statement = request.get("problem_statement")
            if not problem_statement:
                return {"status": "error", "message": "No problem statement provided"}
            
            repo_path = request.get("repo_path")
            if not repo_path:
                return {"status": "error", "message": "No repository path provided"}
            
            model_name = request.get("model_name", "gpt-4")
            
            result = self.swe_agent.run_agent(
                problem_statement=problem_statement,
                repo_path=repo_path,
                model_name=model_name
            )
            
            return {"status": result}
        
        elif command == "stop":
            # Stop the current agent run
            result = self.swe_agent.stop_agent()
            return {"status": "success" if result else "error"}
        
        elif command == "ping":
            # Simple ping to check if the server is running
            return {"status": "success", "message": "pong"}
        
        else:
            return {"status": "error", "message": f"Unknown command: {command}"}


# Singleton instance
vim_integration = VimIntegration()

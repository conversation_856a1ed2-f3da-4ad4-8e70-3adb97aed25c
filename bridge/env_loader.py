"""
Environment variable loader for the AI Coding Agent bridge.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

def load_env_file(env_file: Optional[str] = None) -> Dict[str, str]:
    """
    Load environment variables from a .env file.
    
    Args:
        env_file: Path to the .env file. If None, will look for .env in the project root.
        
    Returns:
        Dictionary of environment variables.
    """
    if env_file is None:
        # Look for .env in the project root
        project_root = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        env_file = project_root / ".env"
    
    env_vars = {}
    
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
                
                # Also set in the environment
                os.environ[key.strip()] = value.strip()
    
    return env_vars

def get_env(key: str, default: Any = None) -> Any:
    """
    Get an environment variable.
    
    Args:
        key: Environment variable key.
        default: Default value if the key is not found.
        
    Returns:
        Environment variable value or default.
    """
    return os.environ.get(key, default)

# Load environment variables when this module is imported
env_vars = load_env_file()

"""
Enhanced API server for the AI Coding Agent bridge with session management.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional

from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room, leave_room

from bridge.config import config
from bridge.session_manager import session_manager, SessionConfig, SessionStatus
from bridge.swe_agent_interface import SWEAgentInterface

# Set up logging
try:
    logging_level = getattr(logging, config.get("logging", "level", default="INFO"))
    log_file = config.get("logging", "file", default="logs/bridge.log")
    os.makedirs(os.path.dirname(log_file), exist_ok=True)

    logging.basicConfig(
        level=logging_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
except Exception:
    # Fallback to basic logging if there's an issue
    logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize SWE-Agent interface (lazy initialization)
swe_agent = None

# Track WebSocket connections
connected_clients = {}


def get_swe_agent():
    """Get SWE-Agent interface with lazy initialization."""
    global swe_agent
    if swe_agent is None:
        swe_agent = SWEAgentInterface()
    return swe_agent


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok", "version": "2.0"})


@app.route('/api/sessions', methods=['POST'])
def create_session():
    """Create a new agent session."""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400
        
        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400
        
        # Create session configuration
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-3-opus-20240229'),
            repo_path=repo_path,
            problem_statement=problem_statement,
            tools=data.get('tools'),
            environment=data.get('environment'),
            retry_config=data.get('retry_config')
        )
        
        # Create session
        session_id = session_manager.create_session(config_data)
        
        return jsonify({
            "session_id": session_id,
            "status": "created"
        }), 201
    
    except Exception as e:
        logger.exception("Error creating session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions', methods=['GET'])
def list_sessions():
    """List all sessions."""
    try:
        status_filter = request.args.get('status')
        status_enum = None
        
        if status_filter:
            try:
                status_enum = SessionStatus(status_filter)
            except ValueError:
                return jsonify({"error": f"Invalid status: {status_filter}"}), 400
        
        sessions = session_manager.list_sessions(status_enum)
        return jsonify({
            "sessions": [session.to_dict() for session in sessions]
        })
    
    except Exception as e:
        logger.exception("Error listing sessions")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get a specific session."""
    try:
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify(session.to_dict())
    
    except Exception as e:
        logger.exception("Error getting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/start', methods=['POST'])
def start_session(session_id):
    """Start a session."""
    try:
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        if session.status != SessionStatus.CREATED:
            return jsonify({"error": f"Session is in {session.status.value} state"}), 400
        
        # Update session status
        session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        
        # Start the agent (this would be done asynchronously in a real implementation)
        result = get_swe_agent().run_agent(
            problem_statement=session.config.problem_statement,
            repo_path=session.config.repo_path,
            model_name=session.config.model_name,
            callback=lambda event, data: _handle_agent_callback(session_id, event, data)
        )
        
        if result == "success":
            return jsonify({"status": "started"})
        else:
            session_manager.update_session_status(session_id, SessionStatus.FAILED, "Failed to start agent")
            return jsonify({"error": "Failed to start agent"}), 500
    
    except Exception as e:
        logger.exception("Error starting session")
        session_manager.update_session_status(session_id, SessionStatus.FAILED, str(e))
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/stop', methods=['POST'])
def stop_session(session_id):
    """Stop a session."""
    try:
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        if session.status != SessionStatus.RUNNING:
            return jsonify({"error": f"Session is not running (status: {session.status.value})"}), 400
        
        # Stop the agent
        result = get_swe_agent().stop_agent()
        
        if result:
            session_manager.update_session_status(session_id, SessionStatus.TERMINATED)
            return jsonify({"status": "stopped"})
        else:
            return jsonify({"error": "Failed to stop agent"}), 500
    
    except Exception as e:
        logger.exception("Error stopping session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/trajectory', methods=['GET'])
def get_session_trajectory(session_id):
    """Get session trajectory."""
    try:
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404
        
        return jsonify({
            "session_id": session_id,
            "trajectory": session.trajectory
        })
    
    except Exception as e:
        logger.exception("Error getting session trajectory")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET'])
def get_config():
    """Get the current configuration."""
    return jsonify(config.config)


# WebSocket event handlers
@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    client_id = request.sid
    connected_clients[client_id] = {
        'connected_at': time.time(),
        'subscribed_sessions': set()
    }
    logger.info(f"Client {client_id} connected")
    emit('connected', {'client_id': client_id})


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    client_id = request.sid
    if client_id in connected_clients:
        # Leave all subscribed session rooms
        for session_id in connected_clients[client_id]['subscribed_sessions']:
            leave_room(f"session_{session_id}")
        
        del connected_clients[client_id]
    
    logger.info(f"Client {client_id} disconnected")


@socketio.on('subscribe_session')
def handle_subscribe_session(data):
    """Subscribe to session updates."""
    client_id = request.sid
    session_id = data.get('session_id')
    
    if not session_id:
        emit('error', {'message': 'No session_id provided'})
        return
    
    # Verify session exists
    session = session_manager.get_session(session_id)
    if not session:
        emit('error', {'message': 'Session not found'})
        return
    
    # Join session room
    join_room(f"session_{session_id}")
    
    # Track subscription
    if client_id in connected_clients:
        connected_clients[client_id]['subscribed_sessions'].add(session_id)
    
    # Register callback for session events
    session_manager.register_callback(session_id, 
        lambda sid, event_type, event_data: _emit_session_event(sid, event_type, event_data))
    
    emit('subscribed', {'session_id': session_id})
    logger.info(f"Client {client_id} subscribed to session {session_id}")


@socketio.on('unsubscribe_session')
def handle_unsubscribe_session(data):
    """Unsubscribe from session updates."""
    client_id = request.sid
    session_id = data.get('session_id')
    
    if not session_id:
        emit('error', {'message': 'No session_id provided'})
        return
    
    # Leave session room
    leave_room(f"session_{session_id}")
    
    # Remove from tracking
    if client_id in connected_clients:
        connected_clients[client_id]['subscribed_sessions'].discard(session_id)
    
    emit('unsubscribed', {'session_id': session_id})
    logger.info(f"Client {client_id} unsubscribed from session {session_id}")


def _handle_agent_callback(session_id: str, event: str, data: Any):
    """Handle callbacks from the SWE-Agent."""
    if event == "progress":
        session_manager.update_session_progress(session_id, data.get('progress', 0.0), data.get('step'))
    elif event == "step":
        session_manager.add_trajectory_step(session_id, data)
    elif event == "completed":
        session_manager.update_session_status(session_id, SessionStatus.COMPLETED)
    elif event == "error":
        session_manager.update_session_status(session_id, SessionStatus.FAILED, data.get('message'))


def _emit_session_event(session_id: str, event_type: str, event_data: Any):
    """Emit session events to subscribed clients."""
    socketio.emit('session_event', {
        'session_id': session_id,
        'event_type': event_type,
        'data': event_data
    }, room=f"session_{session_id}")


def run_server():
    """Run the enhanced API server."""
    host = config.get("bridge", "api_host", default="localhost")
    port = config.get("bridge", "api_port", default=8080)
    
    logger.info(f"Starting enhanced API server on {host}:{port}")
    socketio.run(app, host=host, port=port, debug=True, allow_unsafe_werkzeug=True)


if __name__ == "__main__":
    run_server()

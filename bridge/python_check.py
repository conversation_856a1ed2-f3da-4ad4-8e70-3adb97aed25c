"""
Python version check for the AI Coding Agent bridge.
"""

import sys
import subprocess
from pathlib import Path

def check_python_version():
    """
    Check if the current Python version is compatible with SWE-agent.
    
    Returns:
        bool: True if compatible, False otherwise.
    """
    current_version = sys.version_info
    required_version = (3, 11)
    
    if current_version < required_version:
        print(f"WARNING: SWE-agent requires Python {required_version[0]}.{required_version[1]} or higher.")
        print(f"Current Python version is {current_version.major}.{current_version.minor}.{current_version.micro}")
        return False
    
    return True

def find_python_executable(min_version=(3, 11)):
    """
    Find a Python executable that meets the minimum version requirements.
    
    Args:
        min_version: Minimum required Python version as a tuple (major, minor).
        
    Returns:
        str: Path to the Python executable, or None if not found.
    """
    # Try common Python executable names
    for executable in ["python3.11", "python3.12", "python3.13", "python3"]:
        try:
            # Check if the executable exists and get its version
            result = subprocess.run(
                [executable, "-c", "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')"],
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode == 0:
                version_str = result.stdout.strip()
                try:
                    major, minor = map(int, version_str.split('.'))
                    if (major, minor) >= min_version:
                        return executable
                except ValueError:
                    continue
        except FileNotFoundError:
            continue
    
    return None

if __name__ == "__main__":
    compatible = check_python_version()
    print(f"Python version compatible with SWE-agent: {compatible}")
    
    if not compatible:
        python_exec = find_python_executable()
        if python_exec:
            print(f"Found compatible Python executable: {python_exec}")
        else:
            print("No compatible Python executable found. Please install Python 3.11 or higher.")

"""
Enhanced session management for the AI Coding Agent bridge.
"""

import json
import time
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import threading
import logging

logger = logging.getLogger(__name__)


class SessionStatus(Enum):
    """Session status enumeration."""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TERMINATED = "terminated"


@dataclass
class SessionConfig:
    """Configuration for a session."""
    model_name: str
    repo_path: str
    problem_statement: str
    tools: Optional[Dict[str, Any]] = None
    environment: Optional[Dict[str, Any]] = None
    retry_config: Optional[Dict[str, Any]] = None


@dataclass
class Session:
    """Represents an agent session."""
    session_id: str
    config: SessionConfig
    status: SessionStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    progress: float = 0.0
    current_step: Optional[str] = None
    trajectory: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.trajectory is None:
            self.trajectory = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        return data


class SessionManager:
    """Manages agent sessions."""
    
    def __init__(self):
        """Initialize the session manager."""
        self.sessions: Dict[str, Session] = {}
        self.lock = threading.RLock()
        self.callbacks: Dict[str, List[callable]] = {}
    
    def create_session(self, config: SessionConfig) -> str:
        """
        Create a new session.
        
        Args:
            config: Session configuration.
            
        Returns:
            Session ID.
        """
        session_id = str(uuid.uuid4())
        
        with self.lock:
            session = Session(
                session_id=session_id,
                config=config,
                status=SessionStatus.CREATED,
                created_at=datetime.now()
            )
            self.sessions[session_id] = session
            
        logger.info(f"Created session {session_id}")
        self._notify_callbacks(session_id, "session_created", session.to_dict())
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """
        Get a session by ID.
        
        Args:
            session_id: Session ID.
            
        Returns:
            Session object or None if not found.
        """
        with self.lock:
            return self.sessions.get(session_id)
    
    def list_sessions(self, status: Optional[SessionStatus] = None) -> List[Session]:
        """
        List all sessions, optionally filtered by status.
        
        Args:
            status: Optional status filter.
            
        Returns:
            List of sessions.
        """
        with self.lock:
            sessions = list(self.sessions.values())
            
        if status:
            sessions = [s for s in sessions if s.status == status]
            
        return sorted(sessions, key=lambda s: s.created_at, reverse=True)
    
    def update_session_status(self, session_id: str, status: SessionStatus, 
                            error_message: Optional[str] = None) -> bool:
        """
        Update session status.
        
        Args:
            session_id: Session ID.
            status: New status.
            error_message: Optional error message.
            
        Returns:
            True if successful, False otherwise.
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            session.status = status
            if status == SessionStatus.RUNNING and not session.started_at:
                session.started_at = datetime.now()
            elif status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.TERMINATED]:
                session.completed_at = datetime.now()
            
            if error_message:
                session.error_message = error_message
                
        logger.info(f"Updated session {session_id} status to {status.value}")
        self._notify_callbacks(session_id, "status_changed", {
            "status": status.value,
            "error_message": error_message
        })
        
        return True
    
    def update_session_progress(self, session_id: str, progress: float, 
                              current_step: Optional[str] = None) -> bool:
        """
        Update session progress.
        
        Args:
            session_id: Session ID.
            progress: Progress percentage (0.0 to 1.0).
            current_step: Optional description of current step.
            
        Returns:
            True if successful, False otherwise.
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            session.progress = max(0.0, min(1.0, progress))
            if current_step:
                session.current_step = current_step
                
        self._notify_callbacks(session_id, "progress_updated", {
            "progress": progress,
            "current_step": current_step
        })
        
        return True
    
    def add_trajectory_step(self, session_id: str, step: Dict[str, Any]) -> bool:
        """
        Add a step to the session trajectory.
        
        Args:
            session_id: Session ID.
            step: Trajectory step data.
            
        Returns:
            True if successful, False otherwise.
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            step['timestamp'] = datetime.now().isoformat()
            session.trajectory.append(step)
            
        self._notify_callbacks(session_id, "trajectory_updated", step)
        return True
    
    def terminate_session(self, session_id: str) -> bool:
        """
        Terminate a session.
        
        Args:
            session_id: Session ID.
            
        Returns:
            True if successful, False otherwise.
        """
        return self.update_session_status(session_id, SessionStatus.TERMINATED)
    
    def cleanup_old_sessions(self, max_age_hours: int = 24) -> int:
        """
        Clean up old sessions.
        
        Args:
            max_age_hours: Maximum age in hours.
            
        Returns:
            Number of sessions cleaned up.
        """
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        cleaned_count = 0
        
        with self.lock:
            to_remove = []
            for session_id, session in self.sessions.items():
                if session.created_at.timestamp() < cutoff_time:
                    if session.status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.TERMINATED]:
                        to_remove.append(session_id)
            
            for session_id in to_remove:
                del self.sessions[session_id]
                cleaned_count += 1
                
        logger.info(f"Cleaned up {cleaned_count} old sessions")
        return cleaned_count
    
    def register_callback(self, session_id: str, callback: callable):
        """
        Register a callback for session events.
        
        Args:
            session_id: Session ID.
            callback: Callback function.
        """
        if session_id not in self.callbacks:
            self.callbacks[session_id] = []
        self.callbacks[session_id].append(callback)
    
    def unregister_callback(self, session_id: str, callback: callable):
        """
        Unregister a callback for session events.
        
        Args:
            session_id: Session ID.
            callback: Callback function.
        """
        if session_id in self.callbacks:
            try:
                self.callbacks[session_id].remove(callback)
            except ValueError:
                pass
    
    def _notify_callbacks(self, session_id: str, event_type: str, data: Any):
        """
        Notify registered callbacks of session events.
        
        Args:
            session_id: Session ID.
            event_type: Type of event.
            data: Event data.
        """
        callbacks = self.callbacks.get(session_id, [])
        for callback in callbacks:
            try:
                callback(session_id, event_type, data)
            except Exception as e:
                logger.error(f"Error in session callback: {e}")


# Global session manager instance
session_manager = SessionManager()

"""
Main entry point for the AI Coding Agent bridge.
"""

import argparse
import logging
import sys
import threading
import time
from pathlib import Path

from bridge.config import config
from bridge.api_server import run_server as run_api_server
from bridge.vim_integration import vim_integration


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="AI Coding Agent Bridge")
    parser.add_argument(
        "--config", 
        type=str, 
        help="Path to config file"
    )
    parser.add_argument(
        "--api-only", 
        action="store_true", 
        help="Only start the API server"
    )
    parser.add_argument(
        "--vim-only", 
        action="store_true", 
        help="Only start the Vim integration server"
    )
    parser.add_argument(
        "--api-host", 
        type=str, 
        help="API server host"
    )
    parser.add_argument(
        "--api-port", 
        type=int, 
        help="API server port"
    )
    parser.add_argument(
        "--vim-host", 
        type=str, 
        default="localhost", 
        help="Vim integration server host"
    )
    parser.add_argument(
        "--vim-port", 
        type=int, 
        default=8081, 
        help="Vim integration server port"
    )
    
    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_args()
    
    # Set up logging
    logging_level = getattr(logging, config.get("logging", "level", default="INFO"))
    logging.basicConfig(
        level=logging_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )
    logger = logging.getLogger(__name__)
    
    # Load config if provided
    if args.config:
        from bridge.config import Config
        global config
        config = Config(args.config)
    
    # Override config with command line arguments
    if args.api_host:
        config.config["bridge"]["api_host"] = args.api_host
    if args.api_port:
        config.config["bridge"]["api_port"] = args.api_port
    
    # Start servers based on arguments
    api_thread = None
    
    if not args.vim_only:
        # Start API server in a separate thread
        api_thread = threading.Thread(
            target=run_api_server,
            daemon=True
        )
        api_thread.start()
        logger.info("API server started")
    
    if not args.api_only:
        # Start Vim integration server
        vim_integration.start_server(
            host=args.vim_host,
            port=args.vim_port
        )
    
    try:
        # Keep the main thread running
        while True:
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("Shutting down...")
        
        # Stop Vim integration server
        if not args.api_only:
            vim_integration.stop_server()
        
        # API server will stop when the main thread exits
        sys.exit(0)


if __name__ == "__main__":
    main()

"""
Client for communicating with the Vim plugin.
"""

import json
import socket
import argparse
import sys
from typing import Dict, Any, Optional


def send_command(
    command: str,
    host: str = "localhost",
    port: int = 8081,
    **kwargs
) -> Dict[str, Any]:
    """
    Send a command to the bridge server.
    
    Args:
        command: Command to send.
        host: Host to connect to.
        port: Port to connect to.
        **kwargs: Additional arguments to include in the request.
        
    Returns:
        Response from the server.
    """
    # Create the request
    request = {"command": command, **kwargs}
    
    # Connect to the server
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(10)  # 10 second timeout
            sock.connect((host, port))
            
            # Send the request
            sock.sendall(json.dumps(request).encode("utf-8") + b"\n")
            
            # Receive the response
            data = b""
            while True:
                chunk = sock.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            # Parse the response
            if data:
                return json.loads(data.decode("utf-8"))
            else:
                return {"status": "error", "message": "No response from server"}
    
    except socket.timeout:
        return {"status": "error", "message": "Connection timed out"}
    except ConnectionRefusedError:
        return {"status": "error", "message": "Connection refused"}
    except Exception as e:
        return {"status": "error", "message": str(e)}


def main():
    """Main entry point for the Vim client."""
    parser = argparse.ArgumentParser(description="AI Coding Agent Vim Client")
    parser.add_argument(
        "command",
        choices=["run", "stop", "ping"],
        help="Command to send to the server"
    )
    parser.add_argument(
        "--host",
        default="localhost",
        help="Host to connect to"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8081,
        help="Port to connect to"
    )
    parser.add_argument(
        "--problem-statement",
        help="Problem statement for the 'run' command"
    )
    parser.add_argument(
        "--repo-path",
        help="Repository path for the 'run' command"
    )
    parser.add_argument(
        "--model-name",
        default="gpt-4",
        help="Model name for the 'run' command"
    )
    
    args = parser.parse_args()
    
    # Build the kwargs based on the command
    kwargs = {}
    if args.command == "run":
        if not args.problem_statement:
            print("Error: --problem-statement is required for the 'run' command")
            sys.exit(1)
        if not args.repo_path:
            print("Error: --repo-path is required for the 'run' command")
            sys.exit(1)
        
        kwargs = {
            "problem_statement": args.problem_statement,
            "repo_path": args.repo_path,
            "model_name": args.model_name
        }
    
    # Send the command
    response = send_command(
        command=args.command,
        host=args.host,
        port=args.port,
        **kwargs
    )
    
    # Print the response
    print(json.dumps(response, indent=2))


if __name__ == "__main__":
    main()

"""
API server for the AI Coding Agent bridge.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional

from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>

from bridge.config import config
from bridge.swe_agent_interface import SWEAgentInterface

# Set up logging
logging_level = getattr(logging, config.get("logging", "level", default="INFO"))
log_file = config.get("logging", "file", default="logs/bridge.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize SWE-Agent interface
swe_agent = SWEAgentInterface()


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok"})


@app.route('/api/run', methods=['POST'])
def run_agent():
    """Run the SWE-Agent on a problem statement."""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400
        
        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400
        
        model_name = data.get('model_name', 'gpt-4')
        
        # Run the agent
        result = swe_agent.run_agent(
            problem_statement=problem_statement,
            repo_path=repo_path,
            model_name=model_name
        )
        
        return jsonify({"status": result})
    
    except Exception as e:
        logger.exception("Error running agent")
        return jsonify({"error": str(e)}), 500


@app.route('/api/stop', methods=['POST'])
def stop_agent():
    """Stop the current SWE-Agent run."""
    try:
        result = swe_agent.stop_agent()
        return jsonify({"status": "success" if result else "error"})
    
    except Exception as e:
        logger.exception("Error stopping agent")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET'])
def get_config():
    """Get the current configuration."""
    return jsonify(config.config)


@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    logger.info("Client connected")


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    logger.info("Client disconnected")


def run_server():
    """Run the API server."""
    host = config.get("bridge", "api_host", default="localhost")
    port = config.get("bridge", "api_port", default=8080)
    
    logger.info(f"Starting API server on {host}:{port}")
    socketio.run(app, host=host, port=port, debug=True, allow_unsafe_werkzeug=True)


if __name__ == "__main__":
    run_server()

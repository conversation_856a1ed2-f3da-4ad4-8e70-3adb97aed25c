#!/usr/bin/env python3
"""
Working test for Phase 1 enhanced components.
"""

import sys
import os
import requests
import time
import threading
from pathlib import Path

# Add the project root to the Python path
project_root = Path(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, str(project_root))


def test_session_manager():
    """Test the session manager functionality."""
    print("Testing Session Manager...")
    
    try:
        from bridge.session_manager import session_manager, SessionConfig, SessionStatus
        print("✅ Session manager imported successfully")
        
        # Test session creation
        config = SessionConfig(
            model_name="test-model",
            repo_path="/tmp",
            problem_statement="Test problem statement for Phase 1"
        )
        
        session_id = session_manager.create_session(config)
        print(f"✅ Session created: {session_id}")
        
        # Test session retrieval
        session = session_manager.get_session(session_id)
        if session:
            print(f"✅ Session retrieved: {session.status.value}")
        else:
            print("❌ Failed to retrieve session")
            return False
        
        # Test status update
        success = session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        if success:
            print("✅ Status updated to RUNNING")
        else:
            print("❌ Failed to update status")
            return False
        
        # Test progress update
        success = session_manager.update_session_progress(session_id, 0.5, "Testing progress")
        if success:
            print("✅ Progress updated to 50%")
        else:
            print("❌ Failed to update progress")
            return False
        
        # Test trajectory addition
        success = session_manager.add_trajectory_step(session_id, {
            "action": "test_action",
            "result": "test_result"
        })
        if success:
            print("✅ Trajectory step added")
        else:
            print("❌ Failed to add trajectory step")
            return False
        
        # Test session listing
        sessions = session_manager.list_sessions()
        print(f"✅ Found {len(sessions)} sessions")
        
        # Test session termination
        success = session_manager.terminate_session(session_id)
        if success:
            print("✅ Session terminated successfully")
        else:
            print("❌ Failed to terminate session")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_config():
    """Test the enhanced configuration system."""
    print("\nTesting Enhanced Configuration...")
    
    try:
        from bridge.enhanced_config import EnhancedConfig, EXAMPLE_CONFIGS
        print("✅ Enhanced config imported successfully")
        
        # Test basic configuration
        basic_config = EnhancedConfig.from_dict(EXAMPLE_CONFIGS["basic"])
        print(f"✅ Basic config loaded: {basic_config.agent.model.model_name}")
        
        # Test advanced configuration
        advanced_config = EnhancedConfig.from_dict(EXAMPLE_CONFIGS["advanced"])
        print(f"✅ Advanced config loaded: {len(advanced_config.agent.tools.bundles)} tool bundles")
        
        # Test configuration access
        model_name = basic_config.get("agent", "model", "model_name")
        print(f"✅ Config access works: {model_name}")
        
        # Test SWE-Agent config conversion
        swe_config = basic_config.to_swe_agent_config()
        print(f"✅ SWE-Agent config generated with {len(swe_config['agent']['tools']['bundles'])} tools")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def start_enhanced_server():
    """Start the enhanced API server in a separate thread."""
    try:
        from bridge.enhanced_api_minimal import app
        app.run(host='localhost', port=8080, debug=False, use_reloader=False)
    except Exception as e:
        print(f"Server error: {e}")


def test_enhanced_api():
    """Test the enhanced API endpoints."""
    print("\nTesting Enhanced API...")
    
    try:
        # Start the server in a separate thread
        server_thread = threading.Thread(target=start_enhanced_server, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(3)
        
        base_url = "http://localhost:8080"
        
        # Test health check
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data['status']} v{health_data['version']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
        
        # Test session creation
        session_data = {
            "problem_statement": "Test problem for API",
            "repo_path": "/tmp",
            "model_name": "test-model"
        }
        
        response = requests.post(f"{base_url}/api/sessions", json=session_data, timeout=5)
        if response.status_code == 201:
            session_info = response.json()
            session_id = session_info["session_id"]
            print(f"✅ Session created via API: {session_id}")
        else:
            print(f"❌ Session creation failed: {response.status_code} {response.text}")
            return False
        
        # Test session listing
        response = requests.get(f"{base_url}/api/sessions", timeout=5)
        if response.status_code == 200:
            sessions = response.json()["sessions"]
            print(f"✅ Session listing works: {len(sessions)} sessions")
        else:
            print(f"❌ Session listing failed: {response.status_code}")
            return False
        
        # Test session details
        response = requests.get(f"{base_url}/api/sessions/{session_id}", timeout=5)
        if response.status_code == 200:
            session_details = response.json()
            print(f"✅ Session details retrieved: {session_details['status']}")
        else:
            print(f"❌ Session details failed: {response.status_code}")
            return False
        
        # Test session start
        response = requests.post(f"{base_url}/api/sessions/{session_id}/start", timeout=5)
        if response.status_code == 200:
            start_result = response.json()
            print(f"✅ Session started: {start_result['status']}")
        else:
            print(f"❌ Session start failed: {response.status_code}")
            return False
        
        # Test legacy compatibility
        legacy_data = {
            "problem_statement": "Legacy test",
            "repo_path": "/tmp"
        }
        response = requests.post(f"{base_url}/api/run", json=legacy_data, timeout=5)
        if response.status_code == 200:
            legacy_result = response.json()
            print(f"✅ Legacy endpoint works: {legacy_result['status']}")
        else:
            print(f"❌ Legacy endpoint failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_legacy_compatibility():
    """Test that legacy components still work."""
    print("\nTesting Legacy Compatibility...")
    
    try:
        from bridge.config import config
        print("✅ Legacy config imported successfully")
        
        from bridge.vim_integration import vim_integration
        print("✅ Vim integration imported successfully")
        
        # Test that we can still import the legacy API server
        from bridge.api_server import app as legacy_app
        print("✅ Legacy API server imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Legacy compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all Phase 1 tests."""
    print("🚀 Phase 1 Enhanced Components Test")
    print("=" * 50)
    
    tests = [
        ("Session Manager", test_session_manager),
        ("Enhanced Configuration", test_enhanced_config),
        ("Legacy Compatibility", test_legacy_compatibility),
        ("Enhanced API", test_enhanced_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 Running {test_name} test...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"Phase 1 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 1 tests passed! Enhanced components are working correctly.")
        print("\nPhase 1 Implementation Summary:")
        print("✅ Enhanced session management with multi-session support")
        print("✅ Enhanced configuration system with full SWE-Agent support")
        print("✅ Enhanced API server with session-based endpoints")
        print("✅ Backward compatibility with legacy components")
        print("✅ Real-time session progress tracking")
        return 0
    else:
        print("❌ Some Phase 1 tests failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())

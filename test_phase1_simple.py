#!/usr/bin/env python3
"""
Simple test for Phase 1 enhanced components.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, str(project_root))

def test_session_manager():
    """Test the session manager functionality."""
    print("Testing Session Manager...")
    
    try:
        from bridge.session_manager import session_manager, SessionConfig, SessionStatus
        print("✅ Session manager imported successfully")
        
        # Test session creation
        config = SessionConfig(
            model_name="test-model",
            repo_path="/tmp",
            problem_statement="Test problem statement"
        )
        
        session_id = session_manager.create_session(config)
        print(f"✅ Session created: {session_id}")
        
        # Test session retrieval
        session = session_manager.get_session(session_id)
        if session:
            print(f"✅ Session retrieved: {session.status.value}")
        else:
            print("❌ Failed to retrieve session")
            return False
        
        # Test status update
        success = session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        if success:
            print("✅ Status updated successfully")
        else:
            print("❌ Failed to update status")
            return False
        
        # Test session listing
        sessions = session_manager.list_sessions()
        print(f"✅ Found {len(sessions)} sessions")
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_config():
    """Test the enhanced configuration system."""
    print("\nTesting Enhanced Configuration...")
    
    try:
        from bridge.enhanced_config import EnhancedConfig, EXAMPLE_CONFIGS
        print("✅ Enhanced config imported successfully")
        
        # Test basic configuration
        basic_config = EnhancedConfig.from_dict(EXAMPLE_CONFIGS["basic"])
        print(f"✅ Basic config loaded: {basic_config.agent.model.model_name}")
        
        # Test configuration access
        model_name = basic_config.get("agent", "model", "model_name")
        print(f"✅ Config access works: {model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_server_import():
    """Test importing the enhanced API server."""
    print("\nTesting Enhanced API Server Import...")
    
    try:
        from bridge.enhanced_api_server_simple import app
        print("✅ Enhanced API server imported successfully")
        
        # Test routes
        routes = list(app.url_map.iter_rules())
        print(f"✅ Found {len(routes)} routes:")
        for rule in routes:
            if '/api/' in rule.rule:
                print(f"  {list(rule.methods)} {rule.rule}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced API server test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_legacy_compatibility():
    """Test that legacy components still work."""
    print("\nTesting Legacy Compatibility...")
    
    try:
        from bridge.api_server import app as legacy_app
        print("✅ Legacy API server imported successfully")
        
        from bridge.vim_integration import vim_integration
        print("✅ Vim integration imported successfully")
        
        from bridge.config import config
        print("✅ Legacy config imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Legacy compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Phase 1 Enhanced Components Test")
    print("=" * 50)
    
    tests = [
        test_session_manager,
        test_enhanced_config,
        test_api_server_import,
        test_legacy_compatibility,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All Phase 1 tests passed!")
        return 0
    else:
        print("❌ Some Phase 1 tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())

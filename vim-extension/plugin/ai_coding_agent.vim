" AI Coding Agent integration for Vim
" This file provides integration between the Vim plugin and the AI Coding Agent bridge

if exists('g:loaded_ai_coding_agent')
    finish
endif
let g:loaded_ai_coding_agent = 1

" Default configuration
if !exists('g:ai_coding_agent_host')
    let g:ai_coding_agent_host = 'localhost'
endif

if !exists('g:ai_coding_agent_port')
    let g:ai_coding_agent_port = 8081
endif

if !exists('g:ai_coding_agent_python')
    let g:ai_coding_agent_python = 'python'
endif

if !exists('g:ai_coding_agent_bridge_path')
    let g:ai_coding_agent_bridge_path = expand('<sfile>:p:h:h:h')
endif

" Function to run AI Coding Agent
function! s:RunAICodingAgent(problem_statement) abort
    let repo_path = getcwd()
    let command = g:ai_coding_agent_python . ' ' . g:ai_coding_agent_bridge_path . '/bridge/vim_client.py run'
    let command .= ' --host=' . g:ai_coding_agent_host
    let command .= ' --port=' . g:ai_coding_agent_port
    let command .= ' --problem-statement="' . escape(a:problem_statement, '"') . '"'
    let command .= ' --repo-path="' . escape(repo_path, '"') . '"'
    
    if has('nvim')
        " Neovim async job
        function! s:OnStdout(job_id, data, event) abort
            for line in a:data
                if !empty(line)
                    echom 'AI Coding Agent: ' . line
                endif
            endfor
        endfunction

        function! s:OnExit(job_id, exit_code, event) abort
            if a:exit_code == 0
                echom 'AI Coding Agent: Task completed successfully'
            else
                echom 'AI Coding Agent: Task failed with exit code ' . a:exit_code
            endif
        endfunction

        let job = jobstart(command, {
            \ 'on_stdout': function('s:OnStdout'),
            \ 'on_stderr': function('s:OnStdout'),
            \ 'on_exit': function('s:OnExit'),
            \ })
    else
        " Vim async job
        function! s:OnOut(channel, msg) abort
            echom 'AI Coding Agent: ' . a:msg
        endfunction

        function! s:OnExit(job, status) abort
            if a:status == 0
                echom 'AI Coding Agent: Task completed successfully'
            else
                echom 'AI Coding Agent: Task failed with exit code ' . a:status
            endif
        endfunction

        let job = job_start(command, {
            \ 'out_cb': function('s:OnOut'),
            \ 'err_cb': function('s:OnOut'),
            \ 'exit_cb': function('s:OnExit'),
            \ })
    endif
endfunction

" Function to stop AI Coding Agent
function! s:StopAICodingAgent() abort
    let command = g:ai_coding_agent_python . ' ' . g:ai_coding_agent_bridge_path . '/bridge/vim_client.py stop'
    let command .= ' --host=' . g:ai_coding_agent_host
    let command .= ' --port=' . g:ai_coding_agent_port
    
    if has('nvim')
        " Neovim async job
        let job = jobstart(command)
    else
        " Vim async job
        let job = job_start(command)
    endif
    
    echom 'AI Coding Agent: Stopping current task'
endfunction

" Function to check if AI Coding Agent bridge is running
function! s:PingAICodingAgent() abort
    let command = g:ai_coding_agent_python . ' ' . g:ai_coding_agent_bridge_path . '/bridge/vim_client.py ping'
    let command .= ' --host=' . g:ai_coding_agent_host
    let command .= ' --port=' . g:ai_coding_agent_port
    
    if has('nvim')
        " Neovim async job
        function! s:OnPingStdout(job_id, data, event) abort
            for line in a:data
                if !empty(line) && line =~# '"status": "success"'
                    echom 'AI Coding Agent: Bridge is running'
                    return
                endif
            endfor
            echom 'AI Coding Agent: Bridge is not running'
        endfunction

        let job = jobstart(command, {
            \ 'on_stdout': function('s:OnPingStdout'),
            \ })
    else
        " Vim async job
        function! s:OnPingOut(channel, msg) abort
            if a:msg =~# '"status": "success"'
                echom 'AI Coding Agent: Bridge is running'
                return
            endif
            echom 'AI Coding Agent: Bridge is not running'
        endfunction

        let job = job_start(command, {
            \ 'out_cb': function('s:OnPingOut'),
            \ })
    endif
endfunction

" Define commands
command! -nargs=1 AICodingAgentRun call s:RunAICodingAgent(<q-args>)
command! -nargs=0 AICodingAgentStop call s:StopAICodingAgent()
command! -nargs=0 AICodingAgentPing call s:PingAICodingAgent()

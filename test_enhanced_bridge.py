#!/usr/bin/env python3
"""
Test script for the enhanced AI Coding Agent bridge functionality.
"""

import json
import time
import requests
import socketio
from pathlib import Path
import sys
import argparse

def test_session_management(base_url="http://localhost:8080"):
    """Test the enhanced session management API."""
    print("Testing Enhanced Session Management...")
    
    # Test session creation
    print("1. Creating a new session...")
    session_data = {
        "problem_statement": "Create a simple calculator function",
        "repo_path": str(Path.cwd()),
        "model_name": "claude-3-opus-20240229",
        "tools": {
            "bundles": ["registry", "windowed", "search"],
            "env_variables": {"WINDOW": 100}
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/sessions", json=session_data, timeout=10)
        if response.status_code == 201:
            session_info = response.json()
            session_id = session_info["session_id"]
            print(f"✅ Session created successfully: {session_id}")
        else:
            print(f"❌ Failed to create session: {response.status_code} {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating session: {e}")
        return False
    
    # Test session listing
    print("2. Listing sessions...")
    try:
        response = requests.get(f"{base_url}/api/sessions", timeout=5)
        if response.status_code == 200:
            sessions = response.json()["sessions"]
            print(f"✅ Found {len(sessions)} sessions")
            if sessions:
                print(f"   Latest session: {sessions[0]['session_id']} ({sessions[0]['status']})")
        else:
            print(f"❌ Failed to list sessions: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error listing sessions: {e}")
        return False
    
    # Test session details
    print("3. Getting session details...")
    try:
        response = requests.get(f"{base_url}/api/sessions/{session_id}", timeout=5)
        if response.status_code == 200:
            session_details = response.json()
            print(f"✅ Session details retrieved")
            print(f"   Status: {session_details['status']}")
            print(f"   Created: {session_details['created_at']}")
            print(f"   Problem: {session_details['config']['problem_statement'][:50]}...")
        else:
            print(f"❌ Failed to get session details: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error getting session details: {e}")
        return False
    
    # Test session trajectory
    print("4. Getting session trajectory...")
    try:
        response = requests.get(f"{base_url}/api/sessions/{session_id}/trajectory", timeout=5)
        if response.status_code == 200:
            trajectory = response.json()
            print(f"✅ Trajectory retrieved: {len(trajectory['trajectory'])} steps")
        else:
            print(f"❌ Failed to get trajectory: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error getting trajectory: {e}")
        return False
    
    return True


def test_websocket_functionality(base_url="http://localhost:8080"):
    """Test WebSocket real-time updates."""
    print("\nTesting WebSocket Functionality...")
    
    # Create a session first
    session_data = {
        "problem_statement": "Test WebSocket updates",
        "repo_path": str(Path.cwd()),
        "model_name": "claude-3-opus-20240229"
    }
    
    try:
        response = requests.post(f"{base_url}/api/sessions", json=session_data, timeout=10)
        if response.status_code != 201:
            print(f"❌ Failed to create session for WebSocket test")
            return False
        
        session_id = response.json()["session_id"]
        print(f"Created test session: {session_id}")
    except Exception as e:
        print(f"❌ Error creating session for WebSocket test: {e}")
        return False
    
    # Test WebSocket connection
    print("1. Testing WebSocket connection...")
    events_received = []
    
    def on_connect():
        print("✅ WebSocket connected")
    
    def on_disconnect():
        print("WebSocket disconnected")
    
    def on_session_event(data):
        events_received.append(data)
        print(f"📡 Received session event: {data['event_type']}")
    
    def on_error(data):
        print(f"❌ WebSocket error: {data}")
    
    try:
        # Create socket.io client
        sio = socketio.Client()
        sio.on('connect', on_connect)
        sio.on('disconnect', on_disconnect)
        sio.on('session_event', on_session_event)
        sio.on('error', on_error)
        
        # Connect to server
        sio.connect(base_url)
        
        # Subscribe to session updates
        print("2. Subscribing to session updates...")
        sio.emit('subscribe_session', {'session_id': session_id})
        
        # Wait for subscription confirmation
        time.sleep(1)
        
        # Simulate some session events by making API calls
        print("3. Simulating session events...")
        
        # This would normally trigger WebSocket events in a real implementation
        # For now, we'll just test the connection
        
        time.sleep(2)
        
        # Unsubscribe and disconnect
        sio.emit('unsubscribe_session', {'session_id': session_id})
        sio.disconnect()
        
        print(f"✅ WebSocket test completed. Received {len(events_received)} events")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False


def test_enhanced_configuration():
    """Test the enhanced configuration system."""
    print("\nTesting Enhanced Configuration...")
    
    try:
        from bridge.enhanced_config import EnhancedConfig, EXAMPLE_CONFIGS
        
        # Test basic configuration
        print("1. Testing basic configuration...")
        basic_config = EnhancedConfig.from_dict(EXAMPLE_CONFIGS["basic"])
        print(f"✅ Basic config loaded: {basic_config.agent.model.model_name}")
        
        # Test advanced configuration
        print("2. Testing advanced configuration...")
        advanced_config = EnhancedConfig.from_dict(EXAMPLE_CONFIGS["advanced"])
        print(f"✅ Advanced config loaded: {len(advanced_config.agent.tools.bundles)} tool bundles")
        
        # Test SWE-Agent config conversion
        print("3. Testing SWE-Agent config conversion...")
        swe_config = advanced_config.to_swe_agent_config()
        print(f"✅ SWE-Agent config generated with {len(swe_config['agent']['tools']['bundles'])} tools")
        
        # Test configuration access
        print("4. Testing configuration access...")
        model_name = advanced_config.get("agent", "model", "model_name")
        print(f"✅ Config access works: {model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_session_manager():
    """Test the session manager directly."""
    print("\nTesting Session Manager...")
    
    try:
        from bridge.session_manager import session_manager, SessionConfig, SessionStatus
        
        # Test session creation
        print("1. Testing session creation...")
        config = SessionConfig(
            model_name="test-model",
            repo_path="/tmp",
            problem_statement="Test problem"
        )
        session_id = session_manager.create_session(config)
        print(f"✅ Session created: {session_id}")
        
        # Test session retrieval
        print("2. Testing session retrieval...")
        session = session_manager.get_session(session_id)
        if session:
            print(f"✅ Session retrieved: {session.status.value}")
        else:
            print("❌ Failed to retrieve session")
            return False
        
        # Test status update
        print("3. Testing status update...")
        success = session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        if success:
            print("✅ Status updated successfully")
        else:
            print("❌ Failed to update status")
            return False
        
        # Test progress update
        print("4. Testing progress update...")
        success = session_manager.update_session_progress(session_id, 0.5, "Testing progress")
        if success:
            print("✅ Progress updated successfully")
        else:
            print("❌ Failed to update progress")
            return False
        
        # Test session listing
        print("5. Testing session listing...")
        sessions = session_manager.list_sessions()
        print(f"✅ Found {len(sessions)} sessions")
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager test failed: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Test enhanced bridge functionality")
    parser.add_argument("--base-url", default="http://localhost:8080", help="Base URL for API tests")
    parser.add_argument("--skip-api", action="store_true", help="Skip API tests")
    parser.add_argument("--skip-websocket", action="store_true", help="Skip WebSocket tests")
    parser.add_argument("--skip-config", action="store_true", help="Skip configuration tests")
    parser.add_argument("--skip-session-manager", action="store_true", help="Skip session manager tests")
    
    args = parser.parse_args()
    
    print("🚀 Testing Enhanced AI Coding Agent Bridge")
    print("=" * 50)
    
    success = True
    
    # Test session manager (doesn't require server)
    if not args.skip_session_manager:
        success &= test_session_manager()
    
    # Test configuration system
    if not args.skip_config:
        success &= test_enhanced_configuration()
    
    # Test API endpoints (requires server)
    if not args.skip_api:
        success &= test_session_management(args.base_url)
    
    # Test WebSocket functionality (requires server)
    if not args.skip_websocket:
        success &= test_websocket_functionality(args.base_url)
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())

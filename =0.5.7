Collecting dataclasses-json
  Using cached dataclasses_json-0.6.7-py3-none-any.whl (28 kB)
Requirement already satisfied: python-socketio[client]>=5.8.0 in ./swe_venv/lib/python3.11/site-packages (5.13.0)
Collecting marshmallow<4.0.0,>=3.18.0
  Using cached marshmallow-3.26.1-py3-none-any.whl (50 kB)
Collecting typing-inspect<1,>=0.4.0
  Using cached typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)
Requirement already satisfied: python-engineio>=4.11.0 in ./swe_venv/lib/python3.11/site-packages (from python-socketio[client]>=5.8.0) (4.12.1)
Requirement already satisfied: bidict>=0.21.0 in ./swe_venv/lib/python3.11/site-packages (from python-socketio[client]>=5.8.0) (0.23.1)
Requirement already satisfied: requests>=2.21.0 in ./swe_venv/lib/python3.11/site-packages (from python-socketio[client]>=5.8.0) (2.32.3)
Collecting websocket-client>=0.54.0
  Downloading websocket_client-1.8.0-py3-none-any.whl (58 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 58.8/58.8 KB 1.9 MB/s eta 0:00:00
Requirement already satisfied: packaging>=17.0 in ./swe_venv/lib/python3.11/site-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json) (25.0)
Requirement already satisfied: simple-websocket>=0.10.0 in ./swe_venv/lib/python3.11/site-packages (from python-engineio>=4.11.0->python-socketio[client]>=5.8.0) (1.1.0)
Requirement already satisfied: charset-normalizer<4,>=2 in ./swe_venv/lib/python3.11/site-packages (from requests>=2.21.0->python-socketio[client]>=5.8.0) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in ./swe_venv/lib/python3.11/site-packages (from requests>=2.21.0->python-socketio[client]>=5.8.0) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./swe_venv/lib/python3.11/site-packages (from requests>=2.21.0->python-socketio[client]>=5.8.0) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in ./swe_venv/lib/python3.11/site-packages (from requests>=2.21.0->python-socketio[client]>=5.8.0) (2025.4.26)
Requirement already satisfied: typing-extensions>=3.7.4 in ./swe_venv/lib/python3.11/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json) (4.14.0)
Collecting mypy-extensions>=0.3.0
  Using cached mypy_extensions-1.1.0-py3-none-any.whl (5.0 kB)
Requirement already satisfied: wsproto in ./swe_venv/lib/python3.11/site-packages (from simple-websocket>=0.10.0->python-engineio>=4.11.0->python-socketio[client]>=5.8.0) (1.2.0)
Requirement already satisfied: h11<1,>=0.9.0 in ./swe_venv/lib/python3.11/site-packages (from wsproto->simple-websocket>=0.10.0->python-engineio>=4.11.0->python-socketio[client]>=5.8.0) (0.16.0)
Installing collected packages: websocket-client, mypy-extensions, marshmallow, typing-inspect, dataclasses-json
Successfully installed dataclasses-json-0.6.7 marshmallow-3.26.1 mypy-extensions-1.1.0 typing-inspect-0.9.0 websocket-client-1.8.0
